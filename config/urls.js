module.exports.urls = {
    home_page: {
        hostname: 'sportwrench.com',
        baseUrl: 'https://sportwrench.com'
    },
    main_app: {
        hostname: 'my.sportwrench.com',
        baseUrl: 'https://my.sportwrench.com'
    },
    esw: {
        hostname: 'events.sportwrench.com',
        baseUrl: 'https://events.sportwrench.com'
    },
    swt: {
        hostname: 'tickets.sportwrench.com',
        baseUrl: 'https://tickets.sportwrench.com'
    },
    scores: {
        hostname: 'scores.sportwrench.com',
        baseUrl: 'https://scores.sportwrench.com'
    },
    esw_new: {
        hostname: 'events2.sportwrench.com',
        baseUrl: 'https://events2.sportwrench.com'
    },
    salesHub: {
        hostname: 'sales-hub.swstage.com',
        baseUrl: 'https://sales-hub.swstage.com'
    },
    admin_api_dev: {
        hostname: 'localhost:3000',
        baseUrl: 'http://localhost:3000'
    },
    admin_api_prod: {
        hostname: 'events.sportwrench.com',
        baseUrl: 'https://admin.sportwrench.com'
    },
};
