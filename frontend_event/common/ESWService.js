angular.module('SportWrench')

.factory('eswService', function($http) {

    var currentDivisions = null;

    var _setCurrentDivisions = function(div) {
        currentDivisions = div;
    }

    var _getCurrentDivisions = function() {
        return currentDivisions;
    }

    var _getEvents = function() {
        return $http({
            method: 'GET',
            url: '/api/esw/events'
        })
    };

    var _getEvent = function(id) {
        return $http({
            method: 'GET',
            url: '/api/esw/' + id
        })
    };

    var _getEventDivisions = function(id) {
        return $http({
            method: 'GET',
            url: '/api/esw/' + id + '/divisions'
        })
    };

    var _getEventCourts = function(id) {
        return $http({
            method: 'GET',
            url: '/api/esw/' + id + '/courts'
        })
    };

    var _getEventCourtDetails = function(id, court_id) {
        return $http({
            method: 'GET',
            url: '/api/esw/' + id + '/courts/' + court_id
        })
    };

    var _getEventDivisionStandings = function(id, divId, withCacheCleaning) {
        const queryParam = withCacheCleaning ? '?withCacheCleaning=true' : '';

        return $http({
            method: 'GET',
            url: '/api/esw/' + id + '/divisions/' + divId + '/standings' + queryParam
        })
    };

    var _getEventDivisionTeams = function(id, divId) {
        return $http({
            method: 'GET',
            url: '/api/esw/' + id + '/divisions/' + divId + '/teams'
        })
    };

    var _getPools = function(id, divId) {
        return $http.get('/api/esw/' + id + '/divisions/' + divId + '/pools');
    };

    var _getPoolDetail = function(id, poolId) {
        return $http({
            method: 'GET',
            url: '/api/esw/' + id + '/pools/' + poolId
        })
    };

    var _getEventClubs = function(id) {
        return $http({
            method: 'GET',
            url: '/api/esw/' + id + '/clubs'
        })
    };

    var _getEventClubTeams = function(id, club_id) {
        return $http({
            method: 'GET',
            url: '/api/esw/' + id + '/clubs/' + club_id
        })
    };

    let _getEventAthletes = function(eventID, search) {
        return $http.get('/api/esw/' + eventID + '/athletes', { params: search });
    };

    var _getEventAthleteTeams = function(id, athlete_id) {
        return $http({
            method: 'GET',
            url: '/api/esw/' + id + '/athletes/' + athlete_id
        })
    }; 

    var _getEventStaff = function(id, first, last, team_name) {
        var query = '';

        if (first) {
            query += (!query ? '?' : '') + 'first=' + first;
        } 
        if (last) {
            query += (!query ? '?' : '&') + 'last=' + last;
        } 
        if (team_name) {
            query += (!query ? '?' : '&') + 'team_name=' + team_name;
        }

        return $http({
            method: 'GET',
            url: '/api/esw/' + id + '/staff' + query
        })
    };

    var _getEventStaffTeams = function(id, staff_id) {
        return $http({
            method: 'GET',
            url: '/api/esw/' + id + '/staff/' + staff_id
        })
    };

    var _getEventTeam = function(id, team_id) {
        return $http({
            method: 'GET',
            url: '/api/esw/' + id + '/teams/' + team_id
        })
    }; 

    var _getMatch = function(id, match_id) {
        return $http({
            method: 'GET',
            url: '/api/esw/' + id + '/matches/' + match_id
        })
    };

    var _getStandings = function(id, pool_id) {
        return $http({
            method: 'GET',
            url: '/api/esw/'+id+'/pool/'+pool_id+'/standings'
        })
    }; 

    var _getBracketMatches = function(id, pool_id) {
        return $http({
            method: 'GET',
            url: '/api/esw/' + id + '/brackets/' + pool_id + '/matches'
        })
    }; 

    var _getRosterAthletes = function(id, team_id) {
        return $http({
            method: 'GET',
            url: '/api/esw/' + id + '/roster_athletes?team=' + team_id
        })
    };

    let _getCourtSchedule = function(id, date, hour, hours, division) {
        division = _.isNaN(Number(division)) || _.isNull(division) || division === 0 ? 0 : division;
        return $http.get(`/api/esw/${id}/courts_matches/${date}/hour/${hour}/hours/${hours}/${division}`);
    };

    var _getMatchParams = function(uuid) {
        return $http({
            method: 'GET',
            url: '/api/esw/pb_for_match/' + uuid
        })
    };

    return {
        setCurrentDivisions: _setCurrentDivisions,
        getCurrentDivisions: _getCurrentDivisions,
        getEvents: function(callback) {
            _getEvents().then(function(resp) {
                callback(resp);
            })
        },
        getEvent: function(id, callback) {
            _getEvent(id).then(function(resp) {
                callback(resp);
            })
        },
        getEventDivisions: function(id, callback) {
            _getEventDivisions(id).then(function(resp) {
                callback(resp);
            })
        },
        getEventDivisionStandings: function(id, divId, withCacheCleaning, callback) {
            _getEventDivisionStandings(id, divId, withCacheCleaning).then(function(resp) {
                callback(resp);
            });
        },
        getEventDivisionTeams: function(id, divId, callback) {
            _getEventDivisionTeams(id, divId).then(function(resp) {
                callback(resp);
            });
        },
        getPools: function(id, divId, callback, errorCb) {
            _getPools(id, divId).then(function(resp) {
                if (callback) {
                    callback(resp);
                }
            }, function (reason) {
                if (errorCb) {
                    errorCb(reason);
                }
            });
        },
        getPoolDetail: function(id, poolId, callback) {
            _getPoolDetail(id, poolId).then(function(resp) {
                callback(resp);
            })
        },
        getEventClubs: function(id, callback) {
            _getEventClubs(id).then(function(resp) {
                callback(resp);
            })
        },
        getEventClubTeams: function(id, club_id, callback) {
            _getEventClubTeams(id, club_id).then(function(resp) {
                callback(resp);
            })
        },
        getEventAthletes: function(id, search) {
            return _getEventAthletes(id, search);
        },
        getEventAthleteTeams: function(id, athlete_id, callback) {
            _getEventAthleteTeams(id, athlete_id).then(function(resp) {
                callback(resp);
            })
        },
        getEventStaff: function(id, first, last, team_name, callback) {
            _getEventStaff(id, first, last, team_name).then(function(resp) {
                callback(resp);
            })
        },
        getEventStaffTeams: function(id, staff_id, callback) {
            _getEventStaffTeams(id, staff_id).then(function(resp) {
                callback(resp);
            })
        },
        getEventTeam: function(id, team_id, callback) {
            _getEventTeam(id, team_id).then(function(resp) {
                callback(resp);
            })
        },
        getEventCourts: function(id, callback) {
            _getEventCourts(id).then(function(resp) {
                callback(resp);
            })
        },
        getEventCourtDetails: function(id, court_id, callback) {
            _getEventCourtDetails(id, court_id).then(function(resp) {
                callback(resp);
            })
        },
        getMatch: function(id, match_id, callback) {
            _getMatch(id, match_id).then(function(resp) {
                callback(resp);
            });
        },
        getStandings: function(id, pool_id, callback) {
            _getStandings(id, pool_id).then(function(resp) {
                callback(resp);
            });
        },
        getBracketMatches: function(id, pool_id, callback, errorCb) {
            _getBracketMatches(id, pool_id).then(function(resp) {
                callback(resp);
            }, function (reason) {
                errorCb(reason);
            });
        },
        getRosterAthletes: function(id, team_id, callback) {
            _getRosterAthletes(id, team_id).then(function(resp) {
                callback(resp);
            });
        },
        getCourtSchedule: function(id, date, hour, hours, division) {
            return _getCourtSchedule(id, date, hour, hours, division)
        },
        getMatchParams: function(uuid, successCallback, errorCallback) {
            _getMatchParams(uuid).then(function(resp) {
                successCallback(resp);
            }, function (reason) {
                errorCallback(reason);
            });
        },
        getEventDivisionsList: function(id) {
            return $http.get('/api/esw/club/event/' + id + '/divisions_list')
        },
        isESWId: function (id) {
            return id && /^[0-9A-F]{9}$/i.test(id)
        }
    }
});
