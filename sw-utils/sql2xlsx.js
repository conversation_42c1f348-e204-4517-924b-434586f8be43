#!/usr/bin/env node

const pg = require('pg');
const _ = require('lodash');
const moment = require('moment');
const xlsx = require('xlsx');
const xlsxStyle = require('xlsx-style');
const winston = require('winston');
const logger = new winston.Logger({
    level: 'info',
    transports: [
        new winston.transports.Console({
            timestamp: true
        })
    ]
});

const argv = require('yargs')
    .demandOption('res_file')
    .demandOption('cs')
    .demandOption('base64_sql_query')
    .describe('base64_sql_query', 'Base64 encoded query')
    .describe('cs', 'Connection string')
    .help('h')
    .alias('h', 'help')
    .argv;

main(argv).then(()=>{
    process.exit(0);
}).catch(err=>{
    process.exit(1);
});

async function main(args) {
    logger.info('start');

    try {
        let connection = Buffer.from(args.cs, 'base64').toString('utf-8');

        const client = new pg.Client(JSON.parse(connection));
        await client.connect();

        const data = await execQuery(client, args.base64_sql_query);
        await storeXlsx(args.res_file, data);
    } catch (err) {
        logger.error(err);

        throw err;
    }
}

async function execQuery(client, base64Query) {
    const sql = Buffer.from(base64Query, 'base64').toString('ascii');
    logger.info(sql);
    const data = await client.query(sql);
    logger.info(`Found ${data.rows.length} rows`);
    return data;
}

async function storeXlsx(path, data) {
    let columns = [];
    if (data.rows.length > 0) {
        columns = _.keys(data.rows[0]);
    } else {
        columns = _.map(data.fields, 'name');
    }
    const values = _.map(data.rows, item=> _.map(_.values(item),formatCell));
    data = [columns, ...values];

    const wb = xlsx.utils.book_new();
    const ws = xlsx.utils.aoa_to_sheet(data);
    ws['!cols'] = fitToColumn(data);
    _.forOwn(ws, (cell, key)=>{
        if (/[A-Z]+1$/.test(key)) {
            cell.s = {font: {bold: true}};
        }
    });

    xlsx.utils.book_append_sheet(wb, ws);
    xlsxStyle.writeFileSync(wb, path);
    logger.info('Saved to '+path)
}

function formatCell(value) {
    if (_.isDate(value)) {
        return moment(value).format('DD.MM.YYYY HH:mm:SS');
    } else {
        return value;
    }
}

const fitToColumn = data => {
    const columnWidths = [];
    const [headers] = data;
    for (const header in headers) {
        columnWidths.push({
            wch: Math.max(
                header ? header.toString().length : 0,
                ...data.map(item =>
                    item[header] ? item[header].toString().length : 0
                )
            )
        });
    }
    return columnWidths;
};
