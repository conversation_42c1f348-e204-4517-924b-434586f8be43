let envUrls = process.env.URLS;

try {
    if(envUrls) {
        envUrls = JSON.parse(envUrls);
    }
} catch (err) {
    console.log(err);
    console.error('ENV URLS has unsupported format ' + envUrls);
    envUrls = null;
}

const urls = envUrls || require("../../config/urls").urls;
const { paymentHub: paymentHubConfig } = require("../../config/paymentHub")

module.exports = function(grunt) {
    grunt.config.set('ngconstant', {
        options: {
            name: 'SportWrench',
            dest: 'frontend/env-config/index.js',
            space: '  ',
            deps: false,
            constants: {
                HOME_PAGE_URL: urls.home_page.baseUrl,
                MAIN_APP_URL: urls.main_app.baseUrl,
                ESW_URL: urls.esw.baseUrl,
                ESW_NEW_URL: urls.esw_new.baseUrl,
                SWT_URL: urls.swt.baseUrl,
                SCORES_APP_URL: urls.scores.baseUrl,
                SALES_HUB_URL: urls.salesHub.baseUrl,
                PAYMENT_HUB_API_HOST:   paymentHubConfig.apiUrl,
                PAYMENT_HUB_PUBLISHABLE_KEY: paymentHubConfig.publicKey
            }
        },
        frontend_dev: {
            constants: {
                ENV: 'development',
            }
        },
        frontend_prod: {
            constants: {
                ENV: 'production',
            }
        },
        esw_dev: {
            options: {
                dest: 'frontend_event/env-config/index.js',
            },
            constants: {
                ENV: 'development',
            }
        },
        esw_prod: {
            options: {
                dest: 'frontend_event/env-config/index.js',
            },
            constants: {
                ENV: 'production',
            }
        },
        asw_dev: {
            options: {
                dest: 'frontend_admin/env-config/index.js',
            },
            constants: {
                ENV: 'development',
            }
        },
        asw_prod: {
            options: {
                dest: 'frontend_admin/env-config/index.js',
            },
            constants: {
                ENV: 'production',
            }
        },
    });

    grunt.loadNpmTasks('grunt-ng-constant');    
};
