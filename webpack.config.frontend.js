const path = require('node:path');
const webpack = require('webpack');
const glob = require('glob');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');


const { urls } = require('./config/urls');
const { paymentHub } = require('./config/paymentHub');
const DEV_SERVER_PORT = process.env.PORT || 8079;

const entryFiles = glob.sync([
  './frontend/sport-wrench.module.js',
  './frontend/**/*.module.js',
  './frontend/**/*.constant.js',
  './frontend/**/*.config.js',
  './frontend/**/*.js', 
  './assets/styles/main.scss',
  './assets/styles/admin.scss',
  './assets/stylesEsw/main.scss',
  './assets/stylesTickets/test.scss'
], { absolute: true });

module.exports = (env, argv) => {
  const isProd = argv.mode === 'production';
  return {
    entry: entryFiles,
    
    output: {
      filename: '[name].js',
      path: path.resolve(__dirname, 'dist'),
      publicPath: '/', 
    },
    module: {
      rules: [
        {
          test: /\.js$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: {
              sourceMap: false,
              presets: ['@babel/preset-env'],
              plugins: ['angularjs-annotate'],
            },
          },
        },
        {
          test: /\.scss$/,
          use: [
            MiniCssExtractPlugin.loader,
            'css-loader',
            {
              loader: 'sass-loader',
              options: {
                implementation: require('sass'),
                sourceMap: true,
                sassOptions: {
                  includePaths: ['assets/bower_components', 'assets/styles'],
                },
              },
            },
          ],
        },
        {
          test: /\.css$/,
          use: [MiniCssExtractPlugin.loader, 'css-loader'],
        },
        {
          test: /\.html$/,
          use: ['html-loader'],
        },
      ],
    },
    plugins: [
      new CleanWebpackPlugin(),
      new CopyWebpackPlugin({
        patterns: [
          { from: './assets/bower_components', to: 'bower_components' }, 
          { from: './assets/js', to: 'js' }, 
          { from: './assets/styles', to: 'styles' },
          { from: './assets/images', to: 'images' },
          { from: './frontend', to: '' }, 
        ],
      }),
      new MiniCssExtractPlugin({
        filename: '[name].css',
      }),
        new webpack.DefinePlugin({
        'process.env': {
          NODE_ENV: JSON.stringify(isProd ? 'production' : 'development'),
          HOME_PAGE_URL: JSON.stringify(urls.home_page.baseUrl),
          MAIN_APP_URL: JSON.stringify(urls.main_app.baseUrl),
          ESW_URL: JSON.stringify(urls.esw.baseUrl),
          SWT_URL: JSON.stringify(urls.swt.baseUrl),
          SCORES_APP_URL: JSON.stringify(urls.scores.baseUrl),
          PAYMENT_HUB_API_HOST: JSON.stringify(paymentHub.apiUrl),
          PAYMENT_HUB_PUBLISHABLE_KEY: JSON.stringify(paymentHub.publicKey),
          API_URL: JSON.stringify(isProd ? urls.home_page.baseUrl : `http://localhost:${DEV_SERVER_PORT}`),
        },
      }),
    ],
    optimization: {
      splitChunks: {
        cacheGroups: {
          default: false,
          vendors: false,
        },
      },
      runtimeChunk: false,
      minimize: isProd,
      minimizer: [
        new TerserPlugin({
          exclude: /bower_components/, 
        }),
      ],
    },
    resolve: {
      extensions: ['.js', '.scss', '.css'],
    },
    devServer: {
      static: [
        {
          directory: path.join(__dirname, 'dist'),
          publicPath: '/' 
        },
        {
          directory: path.join(__dirname, 'bower_components'),
          publicPath: '/bower_components/'
        },
      ],
      proxy: [{
        context: ['/api'],
        target: `http://localhost:3000`,
        changeOrigin: true,
      }],
      port: DEV_SERVER_PORT,
      hot: true
    },
    stats: {
      warnings: false, 
    },
    ignoreWarnings: [
      {
        module: /sass-loader/,
        message: /Module Warning/,
      },
    ],
  };
};
